# Generated by Django 5.1.7 on 2025-07-14 06:51

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("app", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="MealLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "timestamp",
                    models.DateTimeField(help_text="When this snapshot was taken"),
                ),
                (
                    "image",
                    models.ImageField(
                        help_text="Top-down food image for this snapshot",
                        upload_to="meal_logs/",
                    ),
                ),
                (
                    "total_weight_g",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total weight on the plate after this snapshot",
                        max_digits=10,
                    ),
                ),
                (
                    "delta_weight_g",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Weight change since previous snapshot",
                        max_digits=10,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("confirmed", "Confirmed"),
                            ("pending_label", "Pending Label"),
                            ("rejected", "Rejected"),
                        ],
                        default="confirmed",
                        help_text="Classification status for this snapshot",
                        max_length=20,
                    ),
                ),
                (
                    "manual_override",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this log was manually corrected",
                    ),
                ),
                (
                    "ai_suggestions",
                    models.JSONField(
                        blank=True,
                        help_text="AI food suggestions with confidence scores",
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name": "Meal Log",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="HistoricalMealSession",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "device_id",
                    models.UUIDField(
                        help_text="ID of the device that recorded this meal session"
                    ),
                ),
                (
                    "start_time",
                    models.DateTimeField(
                        help_text="Timestamp of the first food detection in this session"
                    ),
                ),
                (
                    "end_time",
                    models.DateTimeField(
                        help_text="Timestamp of the last detection or inactivity timeout"
                    ),
                ),
                (
                    "label",
                    models.CharField(
                        choices=[
                            ("breakfast", "Breakfast"),
                            ("lunch", "Lunch"),
                            ("dinner", "Dinner"),
                            ("snack", "Snack"),
                            ("unknown", "Unknown"),
                        ],
                        default="unknown",
                        help_text="User-assigned or auto-inferred label for the meal type",
                        max_length=20,
                    ),
                ),
                (
                    "manual_override",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the session label was manually changed by the user",
                    ),
                ),
                (
                    "total_kcal",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total calories across the meal session",
                        max_digits=10,
                    ),
                ),
                (
                    "protein_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total protein in grams for this session",
                        max_digits=10,
                    ),
                ),
                (
                    "carbs_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total carbohydrates in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "fat_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total fat in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "fibre_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total dietary fiber in grams",
                        max_digits=10,
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="User who consumed the meal",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical Meal Session",
                "verbose_name_plural": "historical Meal Sessions",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalFoodEntry",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "label",
                    models.CharField(
                        help_text="Detected or user-labeled name of the food item",
                        max_length=100,
                    ),
                ),
                (
                    "weight_g",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Portion size in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "estimated_kcal",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Estimated calorie content",
                        max_digits=10,
                    ),
                ),
                (
                    "protein_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Estimated protein content in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "carbs_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Estimated carbohydrate content in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "fat_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Estimated fat content in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "fibre_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Estimated dietary fiber in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        choices=[
                            ("ai_auto", "AI Auto"),
                            ("user_plate", "User Plate"),
                            ("user_app", "User App"),
                        ],
                        default="ai_auto",
                        help_text="Source of the label: AI, plate UI, or mobile app",
                        max_length=20,
                    ),
                ),
                (
                    "manual_override",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the user manually corrected this label",
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "meal_log",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="The meal snapshot this food was detected in",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="app.meallog",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical Food Entry",
                "verbose_name_plural": "historical Food Entrys",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="FoodEntry",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "label",
                    models.CharField(
                        help_text="Detected or user-labeled name of the food item",
                        max_length=100,
                    ),
                ),
                (
                    "weight_g",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Portion size in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "estimated_kcal",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Estimated calorie content",
                        max_digits=10,
                    ),
                ),
                (
                    "protein_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Estimated protein content in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "carbs_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Estimated carbohydrate content in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "fat_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Estimated fat content in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "fibre_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Estimated dietary fiber in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        choices=[
                            ("ai_auto", "AI Auto"),
                            ("user_plate", "User Plate"),
                            ("user_app", "User App"),
                        ],
                        default="ai_auto",
                        help_text="Source of the label: AI, plate UI, or mobile app",
                        max_length=20,
                    ),
                ),
                (
                    "manual_override",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the user manually corrected this label",
                    ),
                ),
                (
                    "meal_log",
                    models.ForeignKey(
                        help_text="The meal snapshot this food was detected in",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="entries",
                        to="app.meallog",
                    ),
                ),
            ],
            options={
                "verbose_name": "Food Entry",
                "ordering": ["-created"],
            },
        ),
        migrations.CreateModel(
            name="MealSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "device_id",
                    models.UUIDField(
                        help_text="ID of the device that recorded this meal session"
                    ),
                ),
                (
                    "start_time",
                    models.DateTimeField(
                        help_text="Timestamp of the first food detection in this session"
                    ),
                ),
                (
                    "end_time",
                    models.DateTimeField(
                        help_text="Timestamp of the last detection or inactivity timeout"
                    ),
                ),
                (
                    "label",
                    models.CharField(
                        choices=[
                            ("breakfast", "Breakfast"),
                            ("lunch", "Lunch"),
                            ("dinner", "Dinner"),
                            ("snack", "Snack"),
                            ("unknown", "Unknown"),
                        ],
                        default="unknown",
                        help_text="User-assigned or auto-inferred label for the meal type",
                        max_length=20,
                    ),
                ),
                (
                    "manual_override",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the session label was manually changed by the user",
                    ),
                ),
                (
                    "total_kcal",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total calories across the meal session",
                        max_digits=10,
                    ),
                ),
                (
                    "protein_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total protein in grams for this session",
                        max_digits=10,
                    ),
                ),
                (
                    "carbs_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total carbohydrates in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "fat_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total fat in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "fibre_g",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total dietary fiber in grams",
                        max_digits=10,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="User who consumed the meal",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="meal_sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Meal Session",
                "ordering": ["-start_time"],
            },
        ),
        migrations.AddField(
            model_name="meallog",
            name="meal_session",
            field=models.ForeignKey(
                help_text="The session this snapshot belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="logs",
                to="app.mealsession",
            ),
        ),
        migrations.CreateModel(
            name="HistoricalMealLog",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "timestamp",
                    models.DateTimeField(help_text="When this snapshot was taken"),
                ),
                (
                    "image",
                    models.TextField(
                        help_text="Top-down food image for this snapshot",
                        max_length=100,
                    ),
                ),
                (
                    "total_weight_g",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total weight on the plate after this snapshot",
                        max_digits=10,
                    ),
                ),
                (
                    "delta_weight_g",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Weight change since previous snapshot",
                        max_digits=10,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("confirmed", "Confirmed"),
                            ("pending_label", "Pending Label"),
                            ("rejected", "Rejected"),
                        ],
                        default="confirmed",
                        help_text="Classification status for this snapshot",
                        max_length=20,
                    ),
                ),
                (
                    "manual_override",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this log was manually corrected",
                    ),
                ),
                (
                    "ai_suggestions",
                    models.JSONField(
                        blank=True,
                        help_text="AI food suggestions with confidence scores",
                        null=True,
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "meal_session",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="The session this snapshot belongs to",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="app.mealsession",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical Meal Log",
                "verbose_name_plural": "historical Meal Logs",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
