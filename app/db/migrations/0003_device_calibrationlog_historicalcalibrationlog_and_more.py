# Generated by Django 5.1.7 on 2025-07-15 07:16

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("app", "0002_meallog_historicalmealsession_historicalfoodentry_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Device",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "device_id",
                    models.CharField(
                        help_text="Public identifier (e.g., serial number or MAC address).",
                        max_length=64,
                        unique=True,
                    ),
                ),
                (
                    "psk_hash",
                    models.CharField(
                        help_text="Hashed pre-shared key used for HMAC-based auth.",
                        max_length=128,
                    ),
                ),
                (
                    "model",
                    models.CharField(
                        help_text="Device model name, e.g., SP-2024A.", max_length=32
                    ),
                ),
                (
                    "firmware_version",
                    models.CharField(
                        help_text="Firmware version currently running on device.",
                        max_length=32,
                    ),
                ),
                (
                    "is_registered",
                    models.BooleanField(
                        default=False,
                        help_text="Indicates if device has connected to the backend.",
                    ),
                ),
                (
                    "last_seen_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Timestamp of the device's last ping.",
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name": "Device",
                "verbose_name_plural": "Devices",
                "ordering": ("-created",),
                "constraints": [
                    models.UniqueConstraint(
                        fields=("device_id",), name="unique_device_id"
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="CalibrationLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When the calibration happened."
                    ),
                ),
                (
                    "zero_offset",
                    models.FloatField(
                        help_text="Recorded zero offset (weight baseline)."
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who initiated calibration.",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="calibrations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "device",
                    models.ForeignKey(
                        help_text="Device being calibrated.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="calibration_logs",
                        to="app.device",
                    ),
                ),
            ],
            options={
                "verbose_name": "Calibration Log",
                "verbose_name_plural": "Calibration Logs",
                "ordering": ("-timestamp",),
            },
        ),
        migrations.CreateModel(
            name="HistoricalCalibrationLog",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        blank=True,
                        editable=False,
                        help_text="When the calibration happened.",
                    ),
                ),
                (
                    "zero_offset",
                    models.FloatField(
                        help_text="Recorded zero offset (weight baseline)."
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "device",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Device being calibrated.",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="app.device",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="User who initiated calibration.",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical Calibration Log",
                "verbose_name_plural": "historical Calibration Logs",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalDevice",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "device_id",
                    models.CharField(
                        db_index=True,
                        help_text="Public identifier (e.g., serial number or MAC address).",
                        max_length=64,
                    ),
                ),
                (
                    "psk_hash",
                    models.CharField(
                        help_text="Hashed pre-shared key used for HMAC-based auth.",
                        max_length=128,
                    ),
                ),
                (
                    "model",
                    models.CharField(
                        help_text="Device model name, e.g., SP-2024A.", max_length=32
                    ),
                ),
                (
                    "firmware_version",
                    models.CharField(
                        help_text="Firmware version currently running on device.",
                        max_length=32,
                    ),
                ),
                (
                    "is_registered",
                    models.BooleanField(
                        default=False,
                        help_text="Indicates if device has connected to the backend.",
                    ),
                ),
                (
                    "last_seen_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Timestamp of the device's last ping.",
                        null=True,
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical Device",
                "verbose_name_plural": "historical Devices",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalDeviceSession",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "access_token",
                    models.CharField(
                        help_text="Ephemeral session token for device authentication.",
                        max_length=128,
                    ),
                ),
                (
                    "nonce_used",
                    models.CharField(
                        help_text="Nonce used to generate HMAC for this session.",
                        max_length=64,
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        help_text="Expiration time of this session token."
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "device",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Device this session belongs to.",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="app.device",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical Device Session",
                "verbose_name_plural": "historical Device Sessions",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalUserDevice",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "is_owner",
                    models.BooleanField(
                        default=False,
                        help_text="Is the user the primary owner of this device?",
                    ),
                ),
                (
                    "calibrated_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Timestamp when device was calibrated by this user.",
                        null=True,
                    ),
                ),
                (
                    "camera_tested",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the user completed a camera test.",
                    ),
                ),
                (
                    "setup_complete",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the user completed device onboarding.",
                    ),
                ),
                (
                    "linked_at",
                    models.DateTimeField(
                        blank=True,
                        editable=False,
                        help_text="Timestamp when user linked this device.",
                    ),
                ),
                (
                    "revoked_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="If revoked, timestamp of unlinking.",
                        null=True,
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "device",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="Device linked to this user.",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="app.device",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="User linked to this device.",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical User Device Link",
                "verbose_name_plural": "historical User Device Links",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="DeviceSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "access_token",
                    models.CharField(
                        help_text="Ephemeral session token for device authentication.",
                        max_length=128,
                    ),
                ),
                (
                    "nonce_used",
                    models.CharField(
                        help_text="Nonce used to generate HMAC for this session.",
                        max_length=64,
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        help_text="Expiration time of this session token."
                    ),
                ),
                (
                    "device",
                    models.ForeignKey(
                        help_text="Device this session belongs to.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sessions",
                        to="app.device",
                    ),
                ),
            ],
            options={
                "verbose_name": "Device Session",
                "verbose_name_plural": "Device Sessions",
                "ordering": ("-created",),
                "constraints": [
                    models.UniqueConstraint(
                        fields=("access_token",), name="unique_device_session_token"
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="UserDevice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "is_owner",
                    models.BooleanField(
                        default=False,
                        help_text="Is the user the primary owner of this device?",
                    ),
                ),
                (
                    "calibrated_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Timestamp when device was calibrated by this user.",
                        null=True,
                    ),
                ),
                (
                    "camera_tested",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the user completed a camera test.",
                    ),
                ),
                (
                    "setup_complete",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the user completed device onboarding.",
                    ),
                ),
                (
                    "linked_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="Timestamp when user linked this device.",
                    ),
                ),
                (
                    "revoked_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="If revoked, timestamp of unlinking.",
                        null=True,
                    ),
                ),
                (
                    "device",
                    models.ForeignKey(
                        help_text="Device linked to this user.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_links",
                        to="app.device",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="User linked to this device.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_devices",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Device Link",
                "verbose_name_plural": "User Device Links",
                "ordering": ("-linked_at",),
                "constraints": [
                    models.UniqueConstraint(
                        fields=("user", "device"), name="unique_user_device_pair"
                    )
                ],
            },
        ),
    ]
