from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

from app.models.base import BaseModel
from app.models.device import Device


class UserDevice(BaseModel):
    """
    Join table representing a user's link to a Smart Plate device,
    including onboarding state.
    """

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="user_devices",
        help_text=_("User linked to this device."),
    )
    device = models.ForeignKey(
        Device,
        on_delete=models.CASCADE,
        related_name="user_links",
        help_text=_("Device linked to this user."),
    )
    is_owner = models.BooleanField(
        default=False,
        help_text=_("Is the user the primary owner of this device?"),
    )
    calibrated_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_("Timestamp when device was calibrated by this user."),
    )
    camera_tested = models.BooleanField(
        default=False,
        help_text=_("Whether the user completed a camera test."),
    )
    setup_complete = models.BooleanField(
        default=False,
        help_text=_("Whether the user completed device onboarding."),
    )
    linked_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_("Timestamp when user linked this device."),
    )
    revoked_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_("If revoked, timestamp of unlinking."),
    )

    class Meta:
        verbose_name = _("User Device Link")
        verbose_name_plural = _("User Device Links")
        ordering = ("-linked_at",)
        constraints = (
            models.UniqueConstraint(
                fields=["user", "device"],
                name="unique_user_device_pair",
            ),
        )

    def __str__(self):
        return f"{self.user} ↔ {self.device.device_id}"
