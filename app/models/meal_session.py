from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models
from django.utils.translation import gettext_lazy as _

from app.models.base import BaseModel


class MealSessionLabel(models.TextChoices):
    BREAKFAST = "breakfast", _("Breakfast")
    LUNCH = "lunch", _("Lunch")
    DINNER = "dinner", _("Dinner")
    SNACK = "snack", _("Snack")
    UNKNOWN = "unknown", _("Unknown")


class MealSession(BaseModel):
    """
    Represents a continuous eating session for a user, such as lunch or dinner.
    Groups together multiple meal logs captured during the same timeframe.
    """

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="meal_sessions",
        help_text=_("User who consumed the meal"),
    )
    device_id = models.UUIDField(
        help_text=_("ID of the device that recorded this meal session"),
    )
    start_time = models.DateTimeField(
        help_text=_("Timestamp of the first food detection in this session"),
    )
    end_time = models.DateTimeField(
        help_text=_("Timestamp of the last detection or inactivity timeout"),
    )
    label = models.CharField(
        max_length=20,
        choices=MealSessionLabel.choices,
        default=MealSessionLabel.UNKNOWN,
        help_text=_("User-assigned or auto-inferred label for the meal type"),
    )
    manual_override = models.BooleanField(
        default=False,
        help_text=_("Whether the session label was manually changed by the user"),
    )
    total_kcal = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text=_("Total calories across the meal session"),
    )
    protein_g = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text=_("Total protein in grams for this session"),
    )
    carbs_g = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text=_("Total carbohydrates in grams"),
    )
    fat_g = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text=_("Total fat in grams"),
    )
    fibre_g = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text=_("Total dietary fiber in grams"),
    )

    class Meta:
        verbose_name = _("Meal Session")
        ordering = ["-start_time"]  # noqa: RUF012

    def __str__(self):
        return f"{self.label} - {self.user} ({self.start_time})"

    def clean(self):
        super().clean()
        if self.end_time and self.start_time and self.end_time <= self.start_time:
            raise ValidationError(_("End time must be after start time"))
