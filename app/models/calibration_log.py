from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

from app.models.base import BaseModel
from app.models.device import Device


class CalibrationLog(BaseModel):
    """
    Audit log for device calibration events.
    """

    device = models.ForeignKey(
        Device,
        on_delete=models.CASCADE,
        related_name="calibration_logs",
        help_text=_("Device being calibrated."),
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="calibrations",
        help_text=_("User who initiated calibration."),
    )
    timestamp = models.DateTimeField(
        auto_now_add=True,
        help_text=_("When the calibration happened."),
    )
    zero_offset = models.FloatField(
        help_text=_("Recorded zero offset (weight baseline)."),
    )

    class Meta:
        verbose_name = _("Calibration Log")
        verbose_name_plural = _("Calibration Logs")
        ordering = ("-timestamp",)

    def __str__(self):
        return f"Calibration @ {self.timestamp:%Y-%m-%d %H:%M}"
