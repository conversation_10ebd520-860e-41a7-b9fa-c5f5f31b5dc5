from django.db import models
from django.utils.translation import gettext_lazy as _

from app.models.base import BaseModel


class Device(BaseModel):
    """
    Represents a Smart Plate device registered in the system.
    """

    device_id = models.CharField(
        max_length=64,
        unique=True,
        help_text=_("Public identifier (e.g., serial number or MAC address)."),
    )
    psk_hash = models.CharField(
        max_length=128,
        help_text=_("Hashed pre-shared key used for HMAC-based auth."),
    )
    model = models.CharField(
        max_length=32,
        help_text=_("Device model name, e.g., SP-2024A."),
    )
    firmware_version = models.Char<PERSON>ield(
        max_length=32,
        help_text=_("Firmware version currently running on device."),
    )
    is_registered = models.BooleanField(
        default=False,
        help_text=_("Indicates if device has connected to the backend."),
    )
    last_seen_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_("Timestamp of the device's last ping."),
    )

    class Meta:
        verbose_name = _("Device")
        verbose_name_plural = _("Devices")
        ordering = ("-created",)
        constraints = (
            models.UniqueConstraint(
                fields=["device_id"],
                name="unique_device_id",
            ),
        )

    def __str__(self):
        return self.device_id
