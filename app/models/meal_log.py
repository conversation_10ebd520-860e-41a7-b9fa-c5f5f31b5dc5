from django.db import models
from django.utils.translation import gettext_lazy as _

from app.models.base import BaseModel
from app.models.meal_session import MealSession


class MealLogStatus(models.TextChoices):
    CONFIRMED = "confirmed", _("Confirmed")
    PENDING_LABEL = "pending_label", _("Pending Label")
    REJECTED = "rejected", _("Rejected")


class MealLog(BaseModel):
    """
    Represents a snapshot of a meal during a session, including food image,
    weight change, and AI predictions.
    """

    meal_session = models.ForeignKey(
        MealSession,
        on_delete=models.CASCADE,
        related_name="logs",
        help_text=_("The session this snapshot belongs to"),
    )
    timestamp = models.DateTimeField(
        help_text=_("When this snapshot was taken"),
    )
    image = models.ImageField(
        upload_to="meal_logs/",
        help_text=_("Top-down food image for this snapshot"),
    )
    total_weight_g = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_("Total weight on the plate after this snapshot"),
    )
    delta_weight_g = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_("Weight change since previous snapshot"),
    )
    status = models.CharField(
        max_length=20,
        choices=MealLogStatus.choices,
        default=MealLogStatus.CONFIRMED,
        help_text=_("Classification status for this snapshot"),
    )
    manual_override = models.BooleanField(
        default=False,
        help_text=_("Whether this log was manually corrected"),
    )
    ai_suggestions = models.JSONField(
        blank=True,
        null=True,
        help_text=_("AI food suggestions with confidence scores"),
    )

    def __str__(self):
        return f"MealLog @ {self.timestamp} ({self.total_weight_g}g)"

    class Meta:
        verbose_name = _("Meal Log")
        ordering = ["-timestamp"]  # noqa: RUF012
