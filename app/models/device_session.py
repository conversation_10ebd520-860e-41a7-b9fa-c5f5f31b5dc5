from django.db import models
from django.utils.translation import gettext_lazy as _

from app.models.base import BaseModel
from app.models.device import Device


class DeviceSession(BaseModel):
    """
    Represents a temporary session token issued to a device
    after HMAC verification.
    """

    device = models.ForeignKey(
        Device,
        on_delete=models.CASCADE,
        related_name="sessions",
        help_text=_("Device this session belongs to."),
    )
    access_token = models.CharField(
        max_length=128,
        help_text=_("Ephemeral session token for device authentication."),
    )
    nonce_used = models.Char<PERSON>ield(
        max_length=64,
        help_text=_("Nonce used to generate HMAC for this session."),
    )
    expires_at = models.DateTimeField(
        help_text=_("Expiration time of this session token."),
    )

    class Meta:
        verbose_name = _("Device Session")
        verbose_name_plural = _("Device Sessions")
        ordering = ("-created",)
        constraints = (
            models.UniqueConstraint(
                fields=["access_token"],
                name="unique_device_session_token",
            ),
        )

    def __str__(self):
        return f"{self.device.device_id} @ {self.created:%Y-%m-%d %H:%M}"
