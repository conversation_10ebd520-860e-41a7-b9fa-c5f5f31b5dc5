"""
User management endpoints for the API.
"""

from fastapi import APIRouter
from fastapi import Depends
from fastapi import HTT<PERSON>Exception
from fastapi import status

from app.api.dependencies.auth import get_current_user
from app.api.schemas.users import User
from app.api.schemas.users import UserCreate
from app.api.schemas.users import UserUpdate

router = APIRouter()

current_user_dependency = Depends(get_current_user)


@router.get("/", response_model=list[User])
async def get_users(current_user: dict = current_user_dependency):
    """Get all users."""
    # In a real application, you would query your Django user model
    # For now, we'll return a mock response
    return [
        {
            "id": 1,
            "username": "testuser",
            "email": "<EMAIL>",
            "is_active": True,
        },
    ]


@router.get("/{user_id}", response_model=User)
async def get_user(user_id: int, current_user: dict = current_user_dependency):
    """Get a specific user by ID."""
    # In a real application, you would query your Django user model
    if user_id != 1:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )

    return {
        "id": user_id,
        "username": "testuser",
        "email": "<EMAIL>",
        "is_active": True,
    }


@router.post("/", response_model=User, status_code=status.HTTP_201_CREATED)
async def create_user(user: UserCreate):
    """Create a new user."""
    # In a real application, you would create a user in your Django model
    # For now, we'll return a mock response
    return {
        "id": 2,
        "username": user.username,
        "email": user.email,
        "is_active": True,
    }


@router.put("/{user_id}", response_model=User)
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    current_user: dict = current_user_dependency,
):
    """Update a user."""
    # In a real application, you would update the user in your Django model
    if user_id != 1:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )

    return {
        "id": user_id,
        "username": user_update.username or "testuser",
        "email": user_update.email or "<EMAIL>",
        "is_active": True,
    }


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(user_id: int, current_user: dict = current_user_dependency):
    """Delete a user."""
    # In a real application, you would delete the user from your Django model
    if user_id != 1:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
