"""
Authentication dependencies for the API.
"""

import environ
from fastapi import Depends
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from fastapi import status
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import JWTError
from jose import jwt

from app.api.schemas.auth import TokenData

env = environ.Env()

SECRET_KEY = env("SECRET_KEY")
ALGORITHM = "HS256"

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/login")


async def get_current_user(token: str = Depends(oauth2_scheme)) -> TokenData:
    """Get current user from JWT token."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except <PERSON><PERSON><PERSON><PERSON><PERSON> as err:
        raise credentials_exception from err

    return token_data
