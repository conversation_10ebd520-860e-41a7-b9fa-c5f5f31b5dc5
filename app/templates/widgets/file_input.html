{% load i18n widget_tweaks %}

<div>
  <label for="{{ field.id_for_label }}"
         class="block text-sm font-medium mb-2 dark:text-white">{{ field.label }}</label>
  <div>
    {% if field.errors %}
      {{ field|add_class:"block w-full text-sm text-gray-500 file:me-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-red-500 file:text-white hover:file:bg-red-600 file:disabled:opacity-50 file:disabled:pointer-events-none dark:text-neutral-500 dark:file:bg-red-500 dark:hover:file:bg-red-400" }}
      {% include "./help_text.html" %}
      {% include "./errors.html" %}
    {% else %}
      {{ field|add_class:"block w-full text-sm text-gray-500 file:me-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-600 file:text-white hover:file:bg-blue-700 file:disabled:opacity-50 file:disabled:pointer-events-none dark:text-neutral-500 dark:file:bg-blue-500 dark:hover:file:bg-blue-400" }}
      {% include "./help_text.html" %}
    {% endif %}
  </div>
</div>
