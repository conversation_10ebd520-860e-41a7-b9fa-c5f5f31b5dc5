{% load i18n widget_tweaks %}

<div class="flex flex-col gap-y-3">
  {% if field.errors %}
    {% for subfield in field %}
      <div class="flex">
        {{ subfield.tag|add_class:"shrink-0 mt-0.5 border-red-500 rounded-full text-blue-600 focus:ring-red-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" }}
        <label for="{{ subfield.id_for_label }}"
               class="text-sm text-red-600 ms-2 dark:text-red-400">{{ subfield.choice_label }}</label>
      </div>
    {% endfor %}
  {% else %}
    {% for subfield in field %}
      <div class="flex">
        {{ subfield.tag|add_class:"shrink-0 mt-0.5 border-gray-200 rounded-full text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" }}
        <label for="{{ subfield.id_for_label }}"
               class="text-sm text-gray-500 ms-2 dark:text-neutral-400">{{ subfield.choice_label }}</label>
      </div>
    {% endfor %}
  {% endif %}
</div>
{% include "./help_text.html" %}
{% include "./errors.html" %}
