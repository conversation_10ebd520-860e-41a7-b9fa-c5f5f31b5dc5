# Smart Plate Backend

Backend API for Smart Plate – handles food recognition, calorie estimation, and meal logging

## Development setup

### Prerequisites

* Python 3.11 or higher
* uv installed (`pip install uv` or follow [installation guide](https://docs.astral.sh/uv/getting-started/installation/))

### Local Development Steps

1. Clone the repo and navigate inside:
   ```bash
   <NAME_EMAIL>:testpress/smartplate_backend.git
   cd smartplate_backend
   ```

2. Install dependencies:
   ```bash
   # Install main dependencies
   uv sync

   # Install development dependencies
   uv sync --extra dev
   ```

3. Activate virtual environment:
   ```bash
   source .venv/bin/activate
   ```

4. Run migrations:
   ```bash
   uv run python manage.py migrate
   ```

5. Start the development server:
   ```bash
   uv run uvicorn config.asgi:application --reload
   ```

   The API documentation will be available at: http://localhost:8000/api/docs

## Running the Project with Docker (Development)

This project uses Docker Compose to run the backend and frontend in development mode with hot reload.

### Prerequisites

* Docker & Docker Compose installed

---

### Steps to Run

1. Clone the repo and navigate inside:

   ```bash
   <NAME_EMAIL>:testpress/smartplate_backend.git
   cd smartplate_backend
   ```

2. Start the development containers with:

   ```bash
   docker-compose -f docker-compose.dev.yml up --build
   ```

3. Collect static files:

   ```bash
   docker-compose -f docker-compose.dev.yml exec web python manage.py collectstatic
   ```

**Run database migrations in a separate terminal (while containers are running):**

   ```bash
   docker-compose -f docker-compose.dev.yml exec web uv run python manage.py migrate
   ```

**To open the Django shell :**

   ```bash
   docker-compose -f docker-compose.dev.yml exec web uv run python manage.py shell
   ```

4. Open your browser and visit:

* Backend API: [http://localhost:8000](http://localhost:8000)

---

### Stopping

To stop the containers, press `CTRL+C` in the terminal or run:

   ```bash
   docker-compose -f docker-compose.dev.yml down
   ```

---

### Notes

* The `docker-compose.dev.yml` file mounts your local code so that changes are reflected immediately.
* Modify ports or environment variables in `.env` or `docker-compose.dev.yml` as needed.

### Troubleshooting Docker Issues

If you encounter issues with missing packages in Docker:

1. **Rebuild containers**: `docker-compose -f docker-compose.dev.yml down && docker-compose -f docker-compose.dev.yml up --build`

2. **Install dev dependencies manually**:
   ```bash
   docker-compose -f docker-compose.dev.yml exec web uv sync --extra dev
   ```

3. **Always use `uv run`**: Commands must be run through uv:
   ```bash
   # ✅ Correct
   docker-compose -f docker-compose.dev.yml exec web uv run python manage.py migrate

   # ❌ Wrong
   docker-compose -f docker-compose.dev.yml exec web python manage.py migrate
   ```

## License

Smart Plate Backend is licensed under the MIT license.
