FROM python:3.13-slim

# Install build tools as root
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential gcc python3-dev \
    && rm -rf /var/lib/apt/lists/*

# Install uv globally
RUN pip install uv

# Create non-root user
RUN useradd -m -u 1000 appuser

# Set working directory
WORKDIR /app

# Copy files
COPY pyproject.toml uv.lock README.md ./

# Install dependencies
RUN uv sync --frozen --extra dev

# Copy the rest of the code
COPY . .

# Change ownership (optional, ensures correct permissions)
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

EXPOSE 8000

# Run the application
CMD ["uvicorn", "config.asgi:application", "--host", "0.0.0.0", "--port", "8000"]
