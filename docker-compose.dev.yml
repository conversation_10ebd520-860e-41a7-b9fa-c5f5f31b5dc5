x-common-env: &common-env
  DJANGO_SETTINGS_MODULE: config.local
  DATABASE_URL: postgres://${POSTGRES_USER:-smartplate}:${POSTGRES_PASSWORD:-testpress1$}@db:5432/${POSTGRES_DB:-smartplate}
  CELERY_BROKER_URL: amqp://rabbitmq:5672

services:
  db:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-smartplate}
      POSTGRES_USER: ${POSTGRES_USER:-smartplate}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-testpress1$}
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-smartplate} -d ${POSTGRES_DB:-smartplate}"]
      interval: 5s
      timeout: 30s
      retries: 5
      start_period: 5s

  web:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - ./logs/celery:/var/log/celery
      - ipython_history:/root/.ipython
    environment:
      <<: *common-env
    depends_on:
      - db
      - rabbitmq
    command: >
      uv run uvicorn config.asgi:main_app
      --host 0.0.0.0
      --port 8000
      --reload

  celery:
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: uv run celery -A config.celery worker -P prefork -l INFO --logfile=/var/log/celery/celery.log
    volumes:
      - .:/app
      - ./logs/celery:/var/log/celery
    environment:
      <<: *common-env
    depends_on:
      - db
      - rabbitmq

  flower:
    image: mher/flower
    command: celery flower --broker=amqp://rabbitmq:5672 --port=5555
    ports:
      - "5555:5555"
    environment:
      <<: *common-env
    depends_on:
      - celery
      - rabbitmq

  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    restart: unless-stopped

volumes:
  postgres_data:
  rabbitmq_data:
  ipython_history:
