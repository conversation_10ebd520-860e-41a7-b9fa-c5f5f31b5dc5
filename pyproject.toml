[project]
name = "smart_plate_backend"
version = "0.1.0"
description = ""
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    # Core Django & dependencies
    "Django==5.1.7",
    "django-environ==0.11.2",
    "django-extensions==3.2.3",
    "django-model-utils==4.5.1",
    "django-widget-tweaks==1.5.0",
    "django-simple-history==3.7.0",
    "ipython==8.26.0",
    "django-safedelete==1.4.0",
    "pillow==10.4.0",
    # Optional Sentry
    "sentry-sdk==2.20.0",
    # Optional FastAPI
    "fastapi[standard]>=0.115.12",
    "uvicorn==0.24.0",
    "python-multipart >= 0.0.18",
    "python-jose[cryptography]==3.3.0",
    "passlib[bcrypt]==1.7.4",
    "python-jose[cryptography]==3.3.0",
    "celery==5.4.0",
    "redis==5.2.1",
    "django-storages>=1.14.6",
    "boto3>=1.39.1",
    "psycopg[binary]>=3.2.9",
]

[project.optional-dependencies]
dev = [
    # Development & testing tools
    "coverage==7.6.1",
    "django-coverage-plugin==3.1.0",
    "django-debug-toolbar==4.4.6",
    "django-stubs==5.0.4",
    "mypy==1.11.1",
    "pre-commit==3.8.0",
    "pytest==8.3.3",
    "pytest-cov==5.0.0",
    "pytest-django==4.8.0",
    "pytest-sugar==1.0.0",
    "ruff",
    "djlint",
    "black",
    "isort",
    "pytest-mock==3.14.1",
    "factory-boy==3.3.3",
    "faker==37.3.0",
    "pytest-xdist==3.7.0",
    "pytest-env==1.1.5",
    "pytest-asyncio==1.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

# ==== pytest ====
[tool.pytest.ini_options]
minversion = "6.0"
addopts = """
--ds=config.test
--reuse-db
--nomigrations
--strict-markers
--disable-warnings
--tb=short
"""
plugins = [
  "pytest-cov",
  "pytest-django",
  "pytest-env",
  "pytest-asyncio"
]
python_files = ["tests.py", "test_*.py", "*_tests.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
env = [
  "DB_NAME=test_lms_db",
]


# ==== Coverage ====
[tool.coverage.run]
include = ["smart_plate_backend/**"]
omit = ["*/migrations/*", "*/tests/*"]
plugins = ["django_coverage_plugin"]


# ==== Ruff ====
[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
    "app/migrations/*.py",
    "app/static/*"
]

# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.12
target-version = "py312"

[tool.ruff.lint]
# https://docs.astral.sh/ruff/rules/
select = [
  "F",
  "E",
  "W",
  "C90",
  "I",
  "N",
  "UP",
  "YTT",
  # "ANN", # flake8-annotations: we should support this in the future but 100+ errors atm
  "ASYNC",
  "S",
  "BLE",
  "FBT",
  "B",
  "A",
  "COM",
  "C4",
  "DTZ",
  "T10",
  "DJ",
  "EM",
  "EXE",
  "FA",
  'ISC',
  "ICN",
  "G",
  'INP',
  'PIE',
  "T20",
  'PYI',
  'PT',
  "Q",
  "RSE",
  "RET",
  "SLF",
  "SLOT",
  "SIM",
  "TID",
  "TCH",
  "INT",
  # "ARG", # Unused function argument
  "PTH",
  "ERA",
  "PD",
  "PGH",
  "PL",
  "TRY",
  "FLY",
  # "NPY",
  # "AIR",
  "PERF",
  # "FURB",
  # "LOG",
  "RUF"
]
ignore = [
  "UP038" # Checks for uses of isinstance/issubclass that take a tuple
          # of types for comparison.
          # Deactivated because it can make the code slow:
          # https://github.com/astral-sh/ruff/issues/7871
]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

[tool.ruff.lint.isort]
force-single-line = true

# ==== mypy ====
[tool.mypy]
python_version = "3.12"
check_untyped_defs = true
ignore_missing_imports = true
strict = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true
plugins = [
    "mypy_django_plugin.main",
]

[[tool.mypy.overrides]]
# Django migrations should not produce any errors:
module = "*.migrations.*"
ignore_errors = true

[tool.django-stubs]
django_settings_module = "config.local"
strict_settings = true

# ==== djLint ====
[tool.djlint]
blank_line_after_tag = "load,extends"
close_void_tags = true
format_css = true
format_js = true
# TODO: remove T002 when fixed https://github.com/djlint/djLint/issues/687
ignore = "H006,H030,H031,T002"
include = "H017,H035"
indent = 2
max_line_length = 119
profile = "django"
[tool.djlint.css]
indent_size = 2

[tool.djlint.js]
indent_size = 2
