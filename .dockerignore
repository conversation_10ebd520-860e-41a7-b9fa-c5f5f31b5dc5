# Git
.git
.gitignore

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Development tools cache
.mypy_cache/
.pytest_cache/
.ruff_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Database
*.sqlite3
db.sqlite3

# Documentation
docs/

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# uv (we only need the lock file)
.venv/

# Testing
tests/
.pytest_cache/

# Local environment files
.env.local
.env.*.local
.env

# Temporary files
*.tmp
*.temp
